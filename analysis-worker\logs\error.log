{"error":"","jobId":"38d9812e-f9a5-4bf6-a1b7-330ea6efde82","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 08:23:04","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"error":"","jobId":"679badfe-2e3d-4650-86fd-5f8c0ae9b26f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 08:25:55","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/83fa5ec5-a72d-4c9e-b39e-39f858577c1c/status"}
{"error":"Request failed with status code 500","jobId":"83fa5ec5-a72d-4c9e-b39e-39f858577c1c","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/839eadab-2d16-475e-b33b-de13158d7100/status"}
{"error":"Request failed with status code 500","jobId":"839eadab-2d16-475e-b33b-de13158d7100","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5/status"}
{"error":"Request failed with status code 500","jobId":"02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/fb78bdb8-234e-4700-b20b-60c402108309/status"}
{"error":"Request failed with status code 500","jobId":"fb78bdb8-234e-4700-b20b-60c402108309","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/4ea9fd5a-a43d-4661-964f-c4e105f820bb/status"}
{"error":"Request failed with status code 500","jobId":"4ea9fd5a-a43d-4661-964f-c4e105f820bb","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/2a089ae6-10f3-4c7e-bcaa-fda238fa30f7/status"}
{"error":"Request failed with status code 500","jobId":"2a089ae6-10f3-4c7e-bcaa-fda238fa30f7","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/5110ad19-a5f9-4094-8eb2-b348dbdbf6b2/status"}
{"error":"Request failed with status code 500","jobId":"5110ad19-a5f9-4094-8eb2-b348dbdbf6b2","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/e07cd077-bfca-4a60-ac00-b971db90cfa7/status"}
{"error":"Request failed with status code 500","jobId":"e07cd077-bfca-4a60-ac00-b971db90cfa7","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/7159b322-1d22-4bde-a29d-1ee19c4c7d67/status"}
{"error":"Request failed with status code 500","jobId":"7159b322-1d22-4bde-a29d-1ee19c4c7d67","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40","url":"/jobs/fd55fe67-45d2-41d6-aa19-eb7a865d67b7/status"}
{"error":"Request failed with status code 500","jobId":"fd55fe67-45d2-41d6-aa19-eb7a865d67b7","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:40"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/ede5ec42-af54-4f93-b936-96f0294ed908/status"}
{"error":"Request failed with status code 500","jobId":"ede5ec42-af54-4f93-b936-96f0294ed908","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/cad0d671-360b-4ebb-b3c9-904e99cfd358/status"}
{"error":"Request failed with status code 500","jobId":"cad0d671-360b-4ebb-b3c9-904e99cfd358","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d/status"}
{"error":"Request failed with status code 500","jobId":"7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/54d11b54-2dc9-4449-92fc-23ec5714f48d/status"}
{"error":"Request failed with status code 500","jobId":"54d11b54-2dc9-4449-92fc-23ec5714f48d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/a6b0e559-4131-4c2f-a76a-f7d2ffde5568/status"}
{"error":"Request failed with status code 500","jobId":"a6b0e559-4131-4c2f-a76a-f7d2ffde5568","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/aa974f0f-1939-4ac3-85e3-14370e3a50e8/status"}
{"error":"Request failed with status code 500","jobId":"aa974f0f-1939-4ac3-85e3-14370e3a50e8","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/5370cd41-c162-47f9-a631-6694cbc7270f/status"}
{"error":"Request failed with status code 500","jobId":"5370cd41-c162-47f9-a631-6694cbc7270f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/08980268-2c3a-4918-8466-cb68f47a3a14/status"}
{"error":"Request failed with status code 500","jobId":"08980268-2c3a-4918-8466-cb68f47a3a14","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/48179fce-84fe-45d9-8187-25035e4f6c02/status"}
{"error":"Request failed with status code 500","jobId":"48179fce-84fe-45d9-8187-25035e4f6c02","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41","url":"/jobs/eae007a5-083d-4d42-b5e5-e07c6793b871/status"}
{"error":"Request failed with status code 500","jobId":"eae007a5-083d-4d42-b5e5-e07c6793b871","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:41"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/b8384c14-6dcb-48f6-8244-197b8c91b124/status"}
{"error":"Request failed with status code 500","jobId":"b8384c14-6dcb-48f6-8244-197b8c91b124","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/94d3de60-906e-4eb7-b7fc-db107419ee9f/status"}
{"error":"Request failed with status code 500","jobId":"94d3de60-906e-4eb7-b7fc-db107419ee9f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/72345d27-301c-4adb-bab4-0e2c8bace3ec/status"}
{"error":"Request failed with status code 500","jobId":"72345d27-301c-4adb-bab4-0e2c8bace3ec","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/9fbccb47-2db1-4d77-8ae7-18fd229bb8ca/status"}
{"error":"Request failed with status code 500","jobId":"9fbccb47-2db1-4d77-8ae7-18fd229bb8ca","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/e8bfd2cf-2507-43d8-b13d-29d7b289884c/status"}
{"error":"Request failed with status code 500","jobId":"e8bfd2cf-2507-43d8-b13d-29d7b289884c","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/ec88b27c-6c71-4758-9857-076020bef935/status"}
{"error":"Request failed with status code 500","jobId":"ec88b27c-6c71-4758-9857-076020bef935","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/b2d1bdb1-51ab-4ee8-8dea-b25b7430737b/status"}
{"error":"Request failed with status code 500","jobId":"b2d1bdb1-51ab-4ee8-8dea-b25b7430737b","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/04934344-c974-491b-b42f-39a50fdfd936/status"}
{"error":"Request failed with status code 500","jobId":"04934344-c974-491b-b42f-39a50fdfd936","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/7120a28e-d49c-46da-b2ac-f232a404c81e/status"}
{"error":"Request failed with status code 500","jobId":"7120a28e-d49c-46da-b2ac-f232a404c81e","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42","url":"/jobs/6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce/status"}
{"error":"Request failed with status code 500","jobId":"6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:42"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/d9d9155a-3e10-4be4-9b76-fa3491570515/status"}
{"error":"Request failed with status code 500","jobId":"d9d9155a-3e10-4be4-9b76-fa3491570515","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/bddd72d4-da03-4a6a-844d-90980dde5bd1/status"}
{"error":"Request failed with status code 500","jobId":"bddd72d4-da03-4a6a-844d-90980dde5bd1","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/c6171c94-e01d-4b7e-b157-207909a9e802/status"}
{"error":"Request failed with status code 500","jobId":"c6171c94-e01d-4b7e-b157-207909a9e802","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/6bbdf453-32d5-407e-8633-77bea75cc502/status"}
{"error":"Request failed with status code 500","jobId":"6bbdf453-32d5-407e-8633-77bea75cc502","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/de40c30c-3db4-44f9-b5df-5b32776f0b54/status"}
{"error":"Request failed with status code 500","jobId":"de40c30c-3db4-44f9-b5df-5b32776f0b54","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/e8450020-0dd6-4c04-b1e0-87ca98fabd94/status"}
{"error":"Request failed with status code 500","jobId":"e8450020-0dd6-4c04-b1e0-87ca98fabd94","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/88de42f4-cd8f-4983-9939-72f9981c615f/status"}
{"error":"Request failed with status code 500","jobId":"88de42f4-cd8f-4983-9939-72f9981c615f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/44c8a7f7-84e1-46ea-bdec-79cb681badca/status"}
{"error":"Request failed with status code 500","jobId":"44c8a7f7-84e1-46ea-bdec-79cb681badca","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/b9c8e7bf-b759-4532-9895-d661f7829e2e/status"}
{"error":"Request failed with status code 500","jobId":"b9c8e7bf-b759-4532-9895-d661f7829e2e","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43","url":"/jobs/bbbbb6ac-4b32-4636-ab07-565207466843/status"}
{"error":"Request failed with status code 500","jobId":"bbbbb6ac-4b32-4636-ab07-565207466843","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:43"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/b6e31852-f889-4eff-b50b-2944c4da572f/status"}
{"error":"Request failed with status code 500","jobId":"b6e31852-f889-4eff-b50b-2944c4da572f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/95de3dfd-bda7-4159-a0eb-d90e036f5b45/status"}
{"error":"Request failed with status code 500","jobId":"95de3dfd-bda7-4159-a0eb-d90e036f5b45","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/9bc9fc6e-8927-4018-83be-a75d9ba59b61/status"}
{"error":"Request failed with status code 500","jobId":"9bc9fc6e-8927-4018-83be-a75d9ba59b61","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00/status"}
{"error":"Request failed with status code 500","jobId":"2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/e451fd7f-880b-4145-915c-21fdee9878a0/status"}
{"error":"Request failed with status code 500","jobId":"e451fd7f-880b-4145-915c-21fdee9878a0","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/8e463987-a216-4a80-9c47-eabb1b4e0f6d/status"}
{"error":"Request failed with status code 500","jobId":"8e463987-a216-4a80-9c47-eabb1b4e0f6d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/3186d36b-2b3e-4861-aa69-1aa06ef90b12/status"}
{"error":"Request failed with status code 500","jobId":"3186d36b-2b3e-4861-aa69-1aa06ef90b12","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/ce18e0db-36f8-434c-8be1-97398bba0c4b/status"}
{"error":"Request failed with status code 500","jobId":"ce18e0db-36f8-434c-8be1-97398bba0c4b","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/f40112ac-cfc1-4734-a5bc-706294f68138/status"}
{"error":"Request failed with status code 500","jobId":"f40112ac-cfc1-4734-a5bc-706294f68138","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44","url":"/jobs/391008b3-59f4-42a4-92e0-beed566628c4/status"}
{"error":"Request failed with status code 500","jobId":"391008b3-59f4-42a4-92e0-beed566628c4","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:44"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58","url":"/jobs/2a089ae6-10f3-4c7e-bcaa-fda238fa30f7/status"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58","url":"/jobs/b2d1bdb1-51ab-4ee8-8dea-b25b7430737b/status"}
{"error":"Request failed with status code 500","jobId":"2a089ae6-10f3-4c7e-bcaa-fda238fa30f7","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 500","jobId":"b2d1bdb1-51ab-4ee8-8dea-b25b7430737b","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58","url":"/jobs/54d11b54-2dc9-4449-92fc-23ec5714f48d/status"}
{"error":"Request failed with status code 500","jobId":"54d11b54-2dc9-4449-92fc-23ec5714f48d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 400","jobId":"2a089ae6-10f3-4c7e-bcaa-fda238fa30f7","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 400","jobId":"b2d1bdb1-51ab-4ee8-8dea-b25b7430737b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 400","jobId":"54d11b54-2dc9-4449-92fc-23ec5714f48d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58","url":"/jobs/7159b322-1d22-4bde-a29d-1ee19c4c7d67/status"}
{"error":"Request failed with status code 500","jobId":"7159b322-1d22-4bde-a29d-1ee19c4c7d67","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 400","jobId":"7159b322-1d22-4bde-a29d-1ee19c4c7d67","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58","url":"/jobs/e07cd077-bfca-4a60-ac00-b971db90cfa7/status"}
{"error":"Request failed with status code 500","jobId":"e07cd077-bfca-4a60-ac00-b971db90cfa7","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 400","jobId":"2a089ae6-10f3-4c7e-bcaa-fda238fa30f7","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:41:58","userId":"f021f043-4935-4be2-b42f-ccdb69d4a5e6"}
{"error":"Request failed with status code 400","jobId":"b2d1bdb1-51ab-4ee8-8dea-b25b7430737b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:41:58","userId":"a41976cc-4a27-44a7-9595-d409e48539be"}
{"error":"Request failed with status code 400","jobId":"e07cd077-bfca-4a60-ac00-b971db90cfa7","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58","url":"/jobs/02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5/status"}
{"error":"Request failed with status code 500","jobId":"02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 400","jobId":"54d11b54-2dc9-4449-92fc-23ec5714f48d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:41:58","userId":"cc739760-9702-414e-95f2-da047d369143"}
{"error":"Request failed with status code 400","jobId":"02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:41:58"}
{"error":"Request failed with status code 400","jobId":"7159b322-1d22-4bde-a29d-1ee19c4c7d67","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:41:58","userId":"b5425449-e8f5-4f48-902a-1ed3df899be9"}
{"error":"Request failed with status code 400","jobId":"e07cd077-bfca-4a60-ac00-b971db90cfa7","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:41:58","userId":"60f711bc-4e2b-448a-9a77-14b54f829e3d"}
{"error":"Request failed with status code 400","jobId":"02ed6e8a-c439-41dc-aebc-38e9e9f1f2c5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:41:58","userId":"323f45ce-4ec5-4858-9a9a-545830e452c8"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/fb78bdb8-234e-4700-b20b-60c402108309/status"}
{"error":"Request failed with status code 500","jobId":"fb78bdb8-234e-4700-b20b-60c402108309","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/b8384c14-6dcb-48f6-8244-197b8c91b124/status"}
{"error":"Request failed with status code 500","jobId":"b8384c14-6dcb-48f6-8244-197b8c91b124","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"fb78bdb8-234e-4700-b20b-60c402108309","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/ede5ec42-af54-4f93-b936-96f0294ed908/status"}
{"error":"Request failed with status code 400","jobId":"b8384c14-6dcb-48f6-8244-197b8c91b124","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","jobId":"ede5ec42-af54-4f93-b936-96f0294ed908","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"fb78bdb8-234e-4700-b20b-60c402108309","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"5fb47e5f-46c6-4d37-8b33-5a235281c777"}
{"error":"Request failed with status code 400","jobId":"ede5ec42-af54-4f93-b936-96f0294ed908","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"b8384c14-6dcb-48f6-8244-197b8c91b124","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"7813975f-f934-41ba-a51a-aae0253074d7"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00/status"}
{"error":"Request failed with status code 400","jobId":"ede5ec42-af54-4f93-b936-96f0294ed908","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"f1c35d09-d758-4c5c-8a95-aa6d30a1c14f"}
{"error":"Request failed with status code 500","jobId":"2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"2ee18f2a-e040-4ef8-bfff-eb0e4fee9e00","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"8da69425-bc20-408b-8ba1-a79b6c2a1e30"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/83fa5ec5-a72d-4c9e-b39e-39f858577c1c/status"}
{"error":"Request failed with status code 500","jobId":"83fa5ec5-a72d-4c9e-b39e-39f858577c1c","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce/status"}
{"error":"Request failed with status code 400","jobId":"83fa5ec5-a72d-4c9e-b39e-39f858577c1c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","jobId":"6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/5370cd41-c162-47f9-a631-6694cbc7270f/status"}
{"error":"Request failed with status code 500","jobId":"5370cd41-c162-47f9-a631-6694cbc7270f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"83fa5ec5-a72d-4c9e-b39e-39f858577c1c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"54568bfb-bab9-480b-be90-e0c4d1ad1e86"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/9bc9fc6e-8927-4018-83be-a75d9ba59b61/status"}
{"error":"Request failed with status code 400","jobId":"5370cd41-c162-47f9-a631-6694cbc7270f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","jobId":"9bc9fc6e-8927-4018-83be-a75d9ba59b61","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"6ad7c259-12bd-46ce-9a64-7bc6bc8e8bce","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"cdf9b892-bf6f-44c0-a555-5a67f2b8691a"}
{"error":"Request failed with status code 400","jobId":"9bc9fc6e-8927-4018-83be-a75d9ba59b61","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"5370cd41-c162-47f9-a631-6694cbc7270f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"93fbd45c-1dc6-4d45-9025-93a2a9378fb4"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/9fbccb47-2db1-4d77-8ae7-18fd229bb8ca/status"}
{"error":"Request failed with status code 400","jobId":"9bc9fc6e-8927-4018-83be-a75d9ba59b61","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"aba0c47a-33b0-443b-a264-5e9053fea0ea"}
{"error":"Request failed with status code 500","jobId":"9fbccb47-2db1-4d77-8ae7-18fd229bb8ca","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"9fbccb47-2db1-4d77-8ae7-18fd229bb8ca","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"9fbccb47-2db1-4d77-8ae7-18fd229bb8ca","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"193e8c90-4efd-476c-b441-0927c6e2f246"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/de40c30c-3db4-44f9-b5df-5b32776f0b54/status"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/08980268-2c3a-4918-8466-cb68f47a3a14/status"}
{"error":"Request failed with status code 500","jobId":"de40c30c-3db4-44f9-b5df-5b32776f0b54","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","jobId":"08980268-2c3a-4918-8466-cb68f47a3a14","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"08980268-2c3a-4918-8466-cb68f47a3a14","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"de40c30c-3db4-44f9-b5df-5b32776f0b54","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"08980268-2c3a-4918-8466-cb68f47a3a14","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"dc6ee2dd-30ae-4188-916d-f5d4ca315f51"}
{"error":"Request failed with status code 400","jobId":"de40c30c-3db4-44f9-b5df-5b32776f0b54","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"b3c545ee-e0c1-4a2c-9219-2ec5ecb2aa12"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/839eadab-2d16-475e-b33b-de13158d7100/status"}
{"error":"Request failed with status code 500","jobId":"839eadab-2d16-475e-b33b-de13158d7100","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"839eadab-2d16-475e-b33b-de13158d7100","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"839eadab-2d16-475e-b33b-de13158d7100","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"f3390657-a2cd-4530-a374-2a3c1f45286a"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/72345d27-301c-4adb-bab4-0e2c8bace3ec/status"}
{"error":"Request failed with status code 500","jobId":"72345d27-301c-4adb-bab4-0e2c8bace3ec","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d/status"}
{"error":"Request failed with status code 400","jobId":"72345d27-301c-4adb-bab4-0e2c8bace3ec","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","jobId":"7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00","url":"/jobs/44c8a7f7-84e1-46ea-bdec-79cb681badca/status"}
{"error":"Request failed with status code 400","jobId":"7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 500","jobId":"44c8a7f7-84e1-46ea-bdec-79cb681badca","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"72345d27-301c-4adb-bab4-0e2c8bace3ec","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"d75127f2-5917-4699-9ce0-231557249954"}
{"error":"Request failed with status code 400","jobId":"44c8a7f7-84e1-46ea-bdec-79cb681badca","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:00"}
{"error":"Request failed with status code 400","jobId":"7e144cb6-3bd4-46c9-ac9c-0ecf9a27b63d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"6dcad023-c1db-4c8f-a1bd-096be3ff44bf"}
{"error":"Request failed with status code 400","jobId":"44c8a7f7-84e1-46ea-bdec-79cb681badca","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:00","userId":"2518e0dc-b877-4165-b464-d6a1d2f9faf5"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/5110ad19-a5f9-4094-8eb2-b348dbdbf6b2/status"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/cad0d671-360b-4ebb-b3c9-904e99cfd358/status"}
{"error":"Request failed with status code 500","jobId":"5110ad19-a5f9-4094-8eb2-b348dbdbf6b2","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"5110ad19-a5f9-4094-8eb2-b348dbdbf6b2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","jobId":"cad0d671-360b-4ebb-b3c9-904e99cfd358","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/eae007a5-083d-4d42-b5e5-e07c6793b871/status"}
{"error":"Request failed with status code 500","jobId":"eae007a5-083d-4d42-b5e5-e07c6793b871","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/ec88b27c-6c71-4758-9857-076020bef935/status"}
{"error":"Request failed with status code 400","jobId":"5110ad19-a5f9-4094-8eb2-b348dbdbf6b2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"156fb4cc-9998-4644-a575-c3838537a679"}
{"error":"Request failed with status code 400","jobId":"cad0d671-360b-4ebb-b3c9-904e99cfd358","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","jobId":"ec88b27c-6c71-4758-9857-076020bef935","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"ec88b27c-6c71-4758-9857-076020bef935","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"cad0d671-360b-4ebb-b3c9-904e99cfd358","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"43c1c6f3-cfdc-4887-adfd-78677a09b7b5"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/3186d36b-2b3e-4861-aa69-1aa06ef90b12/status"}
{"error":"Request failed with status code 500","jobId":"3186d36b-2b3e-4861-aa69-1aa06ef90b12","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"eae007a5-083d-4d42-b5e5-e07c6793b871","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/c6171c94-e01d-4b7e-b157-207909a9e802/status"}
{"error":"Request failed with status code 400","jobId":"ec88b27c-6c71-4758-9857-076020bef935","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"77cbdea1-450d-45ba-ac9b-3d93113fd29f"}
{"error":"Request failed with status code 500","jobId":"c6171c94-e01d-4b7e-b157-207909a9e802","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"3186d36b-2b3e-4861-aa69-1aa06ef90b12","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"eae007a5-083d-4d42-b5e5-e07c6793b871","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"0cfc6127-d5bb-49a8-a535-026b86870f49"}
{"error":"Request failed with status code 400","jobId":"c6171c94-e01d-4b7e-b157-207909a9e802","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"3186d36b-2b3e-4861-aa69-1aa06ef90b12","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"f4adb72f-0300-4429-8025-d28a22a7ef07"}
{"error":"Request failed with status code 400","jobId":"c6171c94-e01d-4b7e-b157-207909a9e802","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"46c862d7-09f2-40bc-8c3b-52e0da194652"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/e8bfd2cf-2507-43d8-b13d-29d7b289884c/status"}
{"error":"Request failed with status code 500","jobId":"e8bfd2cf-2507-43d8-b13d-29d7b289884c","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/04934344-c974-491b-b42f-39a50fdfd936/status"}
{"error":"Request failed with status code 500","jobId":"04934344-c974-491b-b42f-39a50fdfd936","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"e8bfd2cf-2507-43d8-b13d-29d7b289884c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/88de42f4-cd8f-4983-9939-72f9981c615f/status"}
{"error":"Request failed with status code 500","jobId":"88de42f4-cd8f-4983-9939-72f9981c615f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"04934344-c974-491b-b42f-39a50fdfd936","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"88de42f4-cd8f-4983-9939-72f9981c615f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"e8bfd2cf-2507-43d8-b13d-29d7b289884c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"27bcb8fd-4deb-48df-baa3-bff31845502e"}
{"error":"Request failed with status code 400","jobId":"88de42f4-cd8f-4983-9939-72f9981c615f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"de3537d5-6521-4892-8124-28b7fe6dae5d"}
{"error":"Request failed with status code 400","jobId":"04934344-c974-491b-b42f-39a50fdfd936","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"f3fa7032-ae96-4b2d-b425-56589fb77663"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/bddd72d4-da03-4a6a-844d-90980dde5bd1/status"}
{"error":"Request failed with status code 500","jobId":"bddd72d4-da03-4a6a-844d-90980dde5bd1","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/94d3de60-906e-4eb7-b7fc-db107419ee9f/status"}
{"error":"Request failed with status code 400","jobId":"bddd72d4-da03-4a6a-844d-90980dde5bd1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","jobId":"94d3de60-906e-4eb7-b7fc-db107419ee9f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"94d3de60-906e-4eb7-b7fc-db107419ee9f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"bddd72d4-da03-4a6a-844d-90980dde5bd1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"97c9f640-13bf-46fe-9961-686d61bf92f8"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/8e463987-a216-4a80-9c47-eabb1b4e0f6d/status"}
{"error":"Request failed with status code 400","jobId":"94d3de60-906e-4eb7-b7fc-db107419ee9f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"3ac7a54a-9fe1-442b-b19c-446fd3147d78"}
{"error":"Request failed with status code 500","jobId":"8e463987-a216-4a80-9c47-eabb1b4e0f6d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"8e463987-a216-4a80-9c47-eabb1b4e0f6d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/4ea9fd5a-a43d-4661-964f-c4e105f820bb/status"}
{"error":"Request failed with status code 400","jobId":"8e463987-a216-4a80-9c47-eabb1b4e0f6d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"ee83d62e-6847-400d-816e-cc1832f26151"}
{"error":"Request failed with status code 500","jobId":"4ea9fd5a-a43d-4661-964f-c4e105f820bb","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/b9c8e7bf-b759-4532-9895-d661f7829e2e/status"}
{"error":"Request failed with status code 400","jobId":"4ea9fd5a-a43d-4661-964f-c4e105f820bb","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","jobId":"b9c8e7bf-b759-4532-9895-d661f7829e2e","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/7120a28e-d49c-46da-b2ac-f232a404c81e/status"}
{"error":"Request failed with status code 400","jobId":"b9c8e7bf-b759-4532-9895-d661f7829e2e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","jobId":"7120a28e-d49c-46da-b2ac-f232a404c81e","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/ce18e0db-36f8-434c-8be1-97398bba0c4b/status"}
{"error":"Request failed with status code 400","jobId":"7120a28e-d49c-46da-b2ac-f232a404c81e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"4ea9fd5a-a43d-4661-964f-c4e105f820bb","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"73695504-2e17-47c1-b607-e7663f10e766"}
{"error":"Request failed with status code 500","jobId":"ce18e0db-36f8-434c-8be1-97398bba0c4b","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/d9d9155a-3e10-4be4-9b76-fa3491570515/status"}
{"error":"Request failed with status code 400","jobId":"ce18e0db-36f8-434c-8be1-97398bba0c4b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"7120a28e-d49c-46da-b2ac-f232a404c81e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"af9ebb36-ba16-4767-937c-c174d403f9d1"}
{"error":"Request failed with status code 500","jobId":"d9d9155a-3e10-4be4-9b76-fa3491570515","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"b9c8e7bf-b759-4532-9895-d661f7829e2e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"7cc4aa3a-54e6-4693-aefd-6e9b26afdf4f"}
{"error":"Request failed with status code 400","jobId":"ce18e0db-36f8-434c-8be1-97398bba0c4b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"ef9068c3-66dd-4f64-9255-060533918eb8"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/b6e31852-f889-4eff-b50b-2944c4da572f/status"}
{"error":"Request failed with status code 500","jobId":"b6e31852-f889-4eff-b50b-2944c4da572f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/e8450020-0dd6-4c04-b1e0-87ca98fabd94/status"}
{"error":"Request failed with status code 500","jobId":"e8450020-0dd6-4c04-b1e0-87ca98fabd94","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"d9d9155a-3e10-4be4-9b76-fa3491570515","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02","url":"/jobs/e451fd7f-880b-4145-915c-21fdee9878a0/status"}
{"error":"Request failed with status code 500","jobId":"e451fd7f-880b-4145-915c-21fdee9878a0","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"d9d9155a-3e10-4be4-9b76-fa3491570515","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"6db963a7-7828-44db-a3e9-69807a6becb9"}
{"error":"Request failed with status code 400","jobId":"b6e31852-f889-4eff-b50b-2944c4da572f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"e451fd7f-880b-4145-915c-21fdee9878a0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"b6e31852-f889-4eff-b50b-2944c4da572f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"568dc38d-312c-4db0-adf3-18bc743a8317"}
{"error":"Request failed with status code 400","jobId":"e8450020-0dd6-4c04-b1e0-87ca98fabd94","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:02"}
{"error":"Request failed with status code 400","jobId":"e8450020-0dd6-4c04-b1e0-87ca98fabd94","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"896ab44f-9efa-40e9-9487-87ce4bb7a928"}
{"error":"Request failed with status code 400","jobId":"e451fd7f-880b-4145-915c-21fdee9878a0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:02","userId":"87053593-4667-421e-a25c-8c1cbb9a59e5"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04","url":"/jobs/95de3dfd-bda7-4159-a0eb-d90e036f5b45/status"}
{"error":"Request failed with status code 500","jobId":"95de3dfd-bda7-4159-a0eb-d90e036f5b45","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"95de3dfd-bda7-4159-a0eb-d90e036f5b45","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04","url":"/jobs/aa974f0f-1939-4ac3-85e3-14370e3a50e8/status"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04","url":"/jobs/391008b3-59f4-42a4-92e0-beed566628c4/status"}
{"error":"Request failed with status code 500","jobId":"aa974f0f-1939-4ac3-85e3-14370e3a50e8","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 500","jobId":"391008b3-59f4-42a4-92e0-beed566628c4","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04","url":"/jobs/6bbdf453-32d5-407e-8633-77bea75cc502/status"}
{"error":"Request failed with status code 400","jobId":"aa974f0f-1939-4ac3-85e3-14370e3a50e8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 500","jobId":"6bbdf453-32d5-407e-8633-77bea75cc502","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"95de3dfd-bda7-4159-a0eb-d90e036f5b45","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:04","userId":"4cd7f413-0892-4ab7-8e67-e23372284049"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04","url":"/jobs/fd55fe67-45d2-41d6-aa19-eb7a865d67b7/status"}
{"error":"Request failed with status code 400","jobId":"6bbdf453-32d5-407e-8633-77bea75cc502","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"391008b3-59f4-42a4-92e0-beed566628c4","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 500","jobId":"fd55fe67-45d2-41d6-aa19-eb7a865d67b7","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"aa974f0f-1939-4ac3-85e3-14370e3a50e8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:04","userId":"69c1019a-10d2-4fcc-9283-a0623568b127"}
{"error":"Request failed with status code 400","jobId":"6bbdf453-32d5-407e-8633-77bea75cc502","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:04","userId":"a96c4c5d-3a77-4c0f-b4fb-a29118d1e452"}
{"error":"Request failed with status code 400","jobId":"fd55fe67-45d2-41d6-aa19-eb7a865d67b7","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"391008b3-59f4-42a4-92e0-beed566628c4","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:04","userId":"cb2ce36e-31fc-4b3f-9bed-49e9581f04bc"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04","url":"/jobs/48179fce-84fe-45d9-8187-25035e4f6c02/status"}
{"error":"Request failed with status code 500","jobId":"48179fce-84fe-45d9-8187-25035e4f6c02","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"fd55fe67-45d2-41d6-aa19-eb7a865d67b7","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:04","userId":"ec6600d9-60d2-4a98-b71a-9e67941075f6"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04","url":"/jobs/a6b0e559-4131-4c2f-a76a-f7d2ffde5568/status"}
{"error":"Request failed with status code 500","jobId":"a6b0e559-4131-4c2f-a76a-f7d2ffde5568","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"48179fce-84fe-45d9-8187-25035e4f6c02","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"a6b0e559-4131-4c2f-a76a-f7d2ffde5568","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:04"}
{"error":"Request failed with status code 400","jobId":"48179fce-84fe-45d9-8187-25035e4f6c02","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:04","userId":"ff430091-4f15-40d7-8daa-070887dc2ad9"}
{"error":"Request failed with status code 400","jobId":"a6b0e559-4131-4c2f-a76a-f7d2ffde5568","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:04","userId":"d4981c79-f343-4f7a-82cf-fdeed782cb22"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:07","url":"/jobs/bbbbb6ac-4b32-4636-ab07-565207466843/status"}
{"error":"Request failed with status code 500","jobId":"bbbbb6ac-4b32-4636-ab07-565207466843","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:07"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:07","url":"/jobs/f40112ac-cfc1-4734-a5bc-706294f68138/status"}
{"error":"Request failed with status code 400","jobId":"bbbbb6ac-4b32-4636-ab07-565207466843","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:07"}
{"error":"Request failed with status code 500","jobId":"f40112ac-cfc1-4734-a5bc-706294f68138","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-20 10:42:07"}
{"error":"Request failed with status code 400","jobId":"f40112ac-cfc1-4734-a5bc-706294f68138","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-20 10:42:07"}
{"error":"Request failed with status code 400","jobId":"bbbbb6ac-4b32-4636-ab07-565207466843","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:07","userId":"a47cdda2-c722-40a5-ace5-cad082f52367"}
{"error":"Request failed with status code 400","jobId":"f40112ac-cfc1-4734-a5bc-706294f68138","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-20 10:42:07","userId":"126b3f74-3b95-403d-9443-515f2d94109b"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/1aadfd52-29c8-4495-86b1-20dde1bd9126/status"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/8d2e6efd-c1f9-46b1-9826-1e0166f5053c/status"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/fba0c12b-4b25-4b79-9137-0a5c277338ee/status"}
{"error":"timeout of 30000ms exceeded","jobId":"1aadfd52-29c8-4495-86b1-20dde1bd9126","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","jobId":"8d2e6efd-c1f9-46b1-9826-1e0166f5053c","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","jobId":"fba0c12b-4b25-4b79-9137-0a5c277338ee","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/83788dc8-30d0-4915-90d1-7e06be4c4c77/status"}
{"error":"timeout of 30000ms exceeded","jobId":"83788dc8-30d0-4915-90d1-7e06be4c4c77","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/bf174467-d162-4601-8d0c-6753be6cfe7a/status"}
{"error":"timeout of 30000ms exceeded","jobId":"bf174467-d162-4601-8d0c-6753be6cfe7a","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/87a9bf6d-798d-4136-b765-0f20b004f73c/status"}
{"error":"timeout of 30000ms exceeded","jobId":"87a9bf6d-798d-4136-b765-0f20b004f73c","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/fee54607-a66f-4821-bcdc-cb2d5f06b182/status"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/3f452f4a-34ab-4bb5-91c0-0b3be613f4f7/status"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/ea543596-2479-41bc-ae55-c9cdae07a62d/status"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/7bfd0249-93ea-4c3f-b41c-e830dbb17a25/status"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/1d22e705-1fec-41b3-83b5-f8a72d901032/status"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/dfedba4c-10d3-41ca-ab83-990dbcd41dcc/status"}
{"error":"timeout of 30000ms exceeded","jobId":"fee54607-a66f-4821-bcdc-cb2d5f06b182","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/989456bc-1720-45d1-9b93-5db2f0e5e061/status"}
{"error":"timeout of 30000ms exceeded","jobId":"989456bc-1720-45d1-9b93-5db2f0e5e061","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/771d92b1-1597-4aa7-bb32-21ffa557d827/status"}
{"error":"timeout of 30000ms exceeded","jobId":"771d92b1-1597-4aa7-bb32-21ffa557d827","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/525e878b-5fde-45ac-a90f-55cf882460b2/status"}
{"error":"timeout of 30000ms exceeded","jobId":"525e878b-5fde-45ac-a90f-55cf882460b2","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","jobId":"3f452f4a-34ab-4bb5-91c0-0b3be613f4f7","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","jobId":"dfedba4c-10d3-41ca-ab83-990dbcd41dcc","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/30e471d2-1022-4a0c-8e72-5258b6f247f5/status"}
{"error":"timeout of 30000ms exceeded","jobId":"30e471d2-1022-4a0c-8e72-5258b6f247f5","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","jobId":"ea543596-2479-41bc-ae55-c9cdae07a62d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/cd1919fa-4e32-4d02-a96c-5c8e78dc8525/status"}
{"error":"timeout of 30000ms exceeded","jobId":"cd1919fa-4e32-4d02-a96c-5c8e78dc8525","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/493ac0be-3087-4195-8941-74421fdcce8d/status"}
{"error":"timeout of 30000ms exceeded","jobId":"493ac0be-3087-4195-8941-74421fdcce8d","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/7e162a5c-745f-43f9-a277-f6a27a33f968/status"}
{"error":"timeout of 30000ms exceeded","jobId":"7e162a5c-745f-43f9-a277-f6a27a33f968","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","jobId":"7bfd0249-93ea-4c3f-b41c-e830dbb17a25","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/f20ac41a-5f6c-4764-bd63-0bcf8b7e1fdd/status"}
{"error":"timeout of 30000ms exceeded","jobId":"f20ac41a-5f6c-4764-bd63-0bcf8b7e1fdd","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/3a48308d-7654-44ae-b10c-fb71d09f1246/status"}
{"error":"timeout of 30000ms exceeded","jobId":"3a48308d-7654-44ae-b10c-fb71d09f1246","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/961953bd-d5db-4c5f-a73e-b46c679510c5/status"}
{"error":"timeout of 30000ms exceeded","jobId":"961953bd-d5db-4c5f-a73e-b46c679510c5","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/da1fd50e-7b4b-46b2-8b71-9b84f038ff68/status"}
{"error":"timeout of 30000ms exceeded","jobId":"da1fd50e-7b4b-46b2-8b71-9b84f038ff68","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","jobId":"1d22e705-1fec-41b3-83b5-f8a72d901032","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/db19f440-9a6f-4942-b505-d3c1a84b2a3f/status"}
{"error":"timeout of 30000ms exceeded","jobId":"db19f440-9a6f-4942-b505-d3c1a84b2a3f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/8aa45351-4f3c-4e91-bf5c-fddd333b8847/status"}
{"error":"timeout of 30000ms exceeded","jobId":"8aa45351-4f3c-4e91-bf5c-fddd333b8847","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:12","url":"/jobs/31a3c881-1fbe-4cf1-b740-677de42ca185/status"}
{"error":"timeout of 30000ms exceeded","jobId":"31a3c881-1fbe-4cf1-b740-677de42ca185","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","timestamp":"2025-07-20 12:10:12"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"43ac69a7-6b3f-476d-93a8-fbcd6ad6995b","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"1db60450-2ec1-459b-9fbf-5b40bd4fbb39"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"43ac69a7-6b3f-476d-93a8-fbcd6ad6995b","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"1db60450-2ec1-459b-9fbf-5b40bd4fbb39"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"c27af7b8-d308-4712-b255-b0e5abb66cc1","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"879edad7-0025-4739-bfef-e28021ec5035"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"c27af7b8-d308-4712-b255-b0e5abb66cc1","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"879edad7-0025-4739-bfef-e28021ec5035"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"5bf415c3-ad85-481d-a78a-141585ade0c9","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"f620f5a3-d59f-47ef-a85b-82ae1b96c2cd"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"5bf415c3-ad85-481d-a78a-141585ade0c9","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"f620f5a3-d59f-47ef-a85b-82ae1b96c2cd"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"82325f2d-a258-45d3-a39e-b2711a2a306f","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"9017bea0-a3d1-4b7e-a511-a5010fdff1d7"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"82325f2d-a258-45d3-a39e-b2711a2a306f","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"9017bea0-a3d1-4b7e-a511-a5010fdff1d7"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"b832159f-b4af-42db-8490-7dbf7b102fe6","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"01e4d249-c11b-4c1d-aec6-d56c9f16c7d5"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"b832159f-b4af-42db-8490-7dbf7b102fe6","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"01e4d249-c11b-4c1d-aec6-d56c9f16c7d5"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"ca5d757c-99bf-4f54-8b48-017a32aafc06","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"ba9d997e-4692-40e8-901d-7eb986703f9a"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"ca5d757c-99bf-4f54-8b48-017a32aafc06","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"ba9d997e-4692-40e8-901d-7eb986703f9a"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"6251a17e-22d8-4f84-b1b0-86d3afd86c1b","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"53076b75-375f-429a-9701-8d0245fc90f4"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"6251a17e-22d8-4f84-b1b0-86d3afd86c1b","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"53076b75-375f-429a-9701-8d0245fc90f4"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"f7bf8d49-a491-4b56-98ee-e8e8ce0c9354","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"da7ff089-a6f6-4296-9e6a-4f3f974dd00b"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"f7bf8d49-a491-4b56-98ee-e8e8ce0c9354","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"da7ff089-a6f6-4296-9e6a-4f3f974dd00b"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"0b595560-d89d-4621-85e9-3a971e7c8e3f","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"2ad7b940-6f39-4b84-b2f6-4975bfd7c420"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:13"}
{"error":"timeout of 30000ms exceeded","jobId":"0b595560-d89d-4621-85e9-3a971e7c8e3f","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:13","userId":"2ad7b940-6f39-4b84-b2f6-4975bfd7c420"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"a54c546e-6a17-4251-8e9a-1d89927b4597","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"737b9310-3209-4e39-bc95-72c26a389848"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:14"}
{"error":"timeout of 30000ms exceeded","jobId":"a54c546e-6a17-4251-8e9a-1d89927b4597","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"737b9310-3209-4e39-bc95-72c26a389848"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"0b4fad27-0f32-4d03-9d6c-31b09101d72e","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"f64429f3-6c76-427f-94c7-a65f3f58c3b9"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:14"}
{"error":"timeout of 30000ms exceeded","jobId":"0b4fad27-0f32-4d03-9d6c-31b09101d72e","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"f64429f3-6c76-427f-94c7-a65f3f58c3b9"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"272ad54d-8fd2-4a61-be34-e4937f063f25","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"77e61f7f-fa83-43c8-b45d-b90eaa0b488a"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:14"}
{"error":"timeout of 30000ms exceeded","jobId":"272ad54d-8fd2-4a61-be34-e4937f063f25","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"77e61f7f-fa83-43c8-b45d-b90eaa0b488a"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"f7fde668-f823-4666-b49b-24320ef69fa3","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"c78e41a3-7a8d-486f-8b5e-3d32390138a5"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:14"}
{"error":"timeout of 30000ms exceeded","jobId":"f7fde668-f823-4666-b49b-24320ef69fa3","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"c78e41a3-7a8d-486f-8b5e-3d32390138a5"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"10ee28ba-1708-4d3e-a6c0-f263cdec4fca","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"dabbf2c4-343a-4240-97d0-d9acb75cf93e"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:14"}
{"error":"timeout of 30000ms exceeded","jobId":"10ee28ba-1708-4d3e-a6c0-f263cdec4fca","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"dabbf2c4-343a-4240-97d0-d9acb75cf93e"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"ec14e711-fb0c-4f38-aa85-bd075db4380c","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"037517a8-306d-4773-aa2d-36f9bb1bfb61"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:14"}
{"error":"timeout of 30000ms exceeded","jobId":"ec14e711-fb0c-4f38-aa85-bd075db4380c","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:14","userId":"037517a8-306d-4773-aa2d-36f9bb1bfb61"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:15","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:15","userId":"40cd752c-480d-4d15-9169-afc895e2b47c"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:15"}
{"error":"timeout of 30000ms exceeded","jobId":"2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:15","userId":"40cd752c-480d-4d15-9169-afc895e2b47c"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:18","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:18","userId":"fa613df4-9f6d-4a53-99c6-6fd174d07645"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:18"}
{"error":"timeout of 30000ms exceeded","jobId":"a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:18","userId":"fa613df4-9f6d-4a53-99c6-6fd174d07645"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:19","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"eb5ac948-12a1-4edc-bba5-52f7292740a2","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:19","userId":"ffc8fb51-8ec4-4c95-b912-8a8674edbce1"}
{"error":"timeout of 30000ms exceeded","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-20 12:10:19"}
{"error":"timeout of 30000ms exceeded","jobId":"eb5ac948-12a1-4edc-bba5-52f7292740a2","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-20 12:10:19","userId":"ffc8fb51-8ec4-4c95-b912-8a8674edbce1"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"43ac69a7-6b3f-476d-93a8-fbcd6ad6995b","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"1db60450-2ec1-459b-9fbf-5b40bd4fbb39"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/43ac69a7-6b3f-476d-93a8-fbcd6ad6995b/status"}
{"error":"Request failed with status code 400","jobId":"43ac69a7-6b3f-476d-93a8-fbcd6ad6995b","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"43ac69a7-6b3f-476d-93a8-fbcd6ad6995b","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"1db60450-2ec1-459b-9fbf-5b40bd4fbb39"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"43ac69a7-6b3f-476d-93a8-fbcd6ad6995b","level":"error","message":"Failed to process assessment job","processingTime":"79428ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"1db60450-2ec1-459b-9fbf-5b40bd4fbb39"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"43ac69a7-6b3f-476d-93a8-fbcd6ad6995b","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"c27af7b8-d308-4712-b255-b0e5abb66cc1","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"879edad7-0025-4739-bfef-e28021ec5035"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/c27af7b8-d308-4712-b255-b0e5abb66cc1/status"}
{"error":"Request failed with status code 400","jobId":"c27af7b8-d308-4712-b255-b0e5abb66cc1","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"c27af7b8-d308-4712-b255-b0e5abb66cc1","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"879edad7-0025-4739-bfef-e28021ec5035"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"c27af7b8-d308-4712-b255-b0e5abb66cc1","level":"error","message":"Failed to process assessment job","processingTime":"79398ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"879edad7-0025-4739-bfef-e28021ec5035"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"c27af7b8-d308-4712-b255-b0e5abb66cc1","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"5bf415c3-ad85-481d-a78a-141585ade0c9","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"f620f5a3-d59f-47ef-a85b-82ae1b96c2cd"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/5bf415c3-ad85-481d-a78a-141585ade0c9/status"}
{"error":"Request failed with status code 400","jobId":"5bf415c3-ad85-481d-a78a-141585ade0c9","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"5bf415c3-ad85-481d-a78a-141585ade0c9","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"f620f5a3-d59f-47ef-a85b-82ae1b96c2cd"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"5bf415c3-ad85-481d-a78a-141585ade0c9","level":"error","message":"Failed to process assessment job","processingTime":"79269ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"f620f5a3-d59f-47ef-a85b-82ae1b96c2cd"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"5bf415c3-ad85-481d-a78a-141585ade0c9","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"82325f2d-a258-45d3-a39e-b2711a2a306f","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"9017bea0-a3d1-4b7e-a511-a5010fdff1d7"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/82325f2d-a258-45d3-a39e-b2711a2a306f/status"}
{"error":"Request failed with status code 400","jobId":"82325f2d-a258-45d3-a39e-b2711a2a306f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"82325f2d-a258-45d3-a39e-b2711a2a306f","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"9017bea0-a3d1-4b7e-a511-a5010fdff1d7"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"82325f2d-a258-45d3-a39e-b2711a2a306f","level":"error","message":"Failed to process assessment job","processingTime":"79492ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"9017bea0-a3d1-4b7e-a511-a5010fdff1d7"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"82325f2d-a258-45d3-a39e-b2711a2a306f","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"b832159f-b4af-42db-8490-7dbf7b102fe6","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"01e4d249-c11b-4c1d-aec6-d56c9f16c7d5"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/b832159f-b4af-42db-8490-7dbf7b102fe6/status"}
{"error":"Request failed with status code 400","jobId":"b832159f-b4af-42db-8490-7dbf7b102fe6","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"b832159f-b4af-42db-8490-7dbf7b102fe6","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"01e4d249-c11b-4c1d-aec6-d56c9f16c7d5"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"b832159f-b4af-42db-8490-7dbf7b102fe6","level":"error","message":"Failed to process assessment job","processingTime":"79845ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"01e4d249-c11b-4c1d-aec6-d56c9f16c7d5"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"b832159f-b4af-42db-8490-7dbf7b102fe6","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"ca5d757c-99bf-4f54-8b48-017a32aafc06","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"ba9d997e-4692-40e8-901d-7eb986703f9a"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/ca5d757c-99bf-4f54-8b48-017a32aafc06/status"}
{"error":"Request failed with status code 400","jobId":"ca5d757c-99bf-4f54-8b48-017a32aafc06","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"ca5d757c-99bf-4f54-8b48-017a32aafc06","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"ba9d997e-4692-40e8-901d-7eb986703f9a"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"ca5d757c-99bf-4f54-8b48-017a32aafc06","level":"error","message":"Failed to process assessment job","processingTime":"79726ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"ba9d997e-4692-40e8-901d-7eb986703f9a"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"ca5d757c-99bf-4f54-8b48-017a32aafc06","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"6251a17e-22d8-4f84-b1b0-86d3afd86c1b","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"53076b75-375f-429a-9701-8d0245fc90f4"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/6251a17e-22d8-4f84-b1b0-86d3afd86c1b/status"}
{"error":"Request failed with status code 400","jobId":"6251a17e-22d8-4f84-b1b0-86d3afd86c1b","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"6251a17e-22d8-4f84-b1b0-86d3afd86c1b","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"53076b75-375f-429a-9701-8d0245fc90f4"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"6251a17e-22d8-4f84-b1b0-86d3afd86c1b","level":"error","message":"Failed to process assessment job","processingTime":"79706ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"53076b75-375f-429a-9701-8d0245fc90f4"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"6251a17e-22d8-4f84-b1b0-86d3afd86c1b","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"f7bf8d49-a491-4b56-98ee-e8e8ce0c9354","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"da7ff089-a6f6-4296-9e6a-4f3f974dd00b"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/f7bf8d49-a491-4b56-98ee-e8e8ce0c9354/status"}
{"error":"Request failed with status code 400","jobId":"f7bf8d49-a491-4b56-98ee-e8e8ce0c9354","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"f7bf8d49-a491-4b56-98ee-e8e8ce0c9354","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"da7ff089-a6f6-4296-9e6a-4f3f974dd00b"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"f7bf8d49-a491-4b56-98ee-e8e8ce0c9354","level":"error","message":"Failed to process assessment job","processingTime":"79882ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"da7ff089-a6f6-4296-9e6a-4f3f974dd00b"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"f7bf8d49-a491-4b56-98ee-e8e8ce0c9354","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"0b595560-d89d-4621-85e9-3a971e7c8e3f","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"2ad7b940-6f39-4b84-b2f6-4975bfd7c420"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43","url":"/jobs/0b595560-d89d-4621-85e9-3a971e7c8e3f/status"}
{"error":"Request failed with status code 400","jobId":"0b595560-d89d-4621-85e9-3a971e7c8e3f","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:43"}
{"error":"Request failed with status code 400","jobId":"0b595560-d89d-4621-85e9-3a971e7c8e3f","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"2ad7b940-6f39-4b84-b2f6-4975bfd7c420"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"0b595560-d89d-4621-85e9-3a971e7c8e3f","level":"error","message":"Failed to process assessment job","processingTime":"79956ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:43","userId":"2ad7b940-6f39-4b84-b2f6-4975bfd7c420"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"0b595560-d89d-4621-85e9-3a971e7c8e3f","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:43"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"a54c546e-6a17-4251-8e9a-1d89927b4597","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"737b9310-3209-4e39-bc95-72c26a389848"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44","url":"/jobs/a54c546e-6a17-4251-8e9a-1d89927b4597/status"}
{"error":"Request failed with status code 400","jobId":"a54c546e-6a17-4251-8e9a-1d89927b4597","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44"}
{"error":"Request failed with status code 400","jobId":"a54c546e-6a17-4251-8e9a-1d89927b4597","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"737b9310-3209-4e39-bc95-72c26a389848"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"a54c546e-6a17-4251-8e9a-1d89927b4597","level":"error","message":"Failed to process assessment job","processingTime":"80394ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"737b9310-3209-4e39-bc95-72c26a389848"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"a54c546e-6a17-4251-8e9a-1d89927b4597","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:44"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"0b4fad27-0f32-4d03-9d6c-31b09101d72e","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"f64429f3-6c76-427f-94c7-a65f3f58c3b9"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"272ad54d-8fd2-4a61-be34-e4937f063f25","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"77e61f7f-fa83-43c8-b45d-b90eaa0b488a"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44","url":"/jobs/0b4fad27-0f32-4d03-9d6c-31b09101d72e/status"}
{"error":"Request failed with status code 400","jobId":"0b4fad27-0f32-4d03-9d6c-31b09101d72e","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44","url":"/jobs/272ad54d-8fd2-4a61-be34-e4937f063f25/status"}
{"error":"Request failed with status code 400","jobId":"272ad54d-8fd2-4a61-be34-e4937f063f25","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44"}
{"error":"Request failed with status code 400","jobId":"0b4fad27-0f32-4d03-9d6c-31b09101d72e","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"f64429f3-6c76-427f-94c7-a65f3f58c3b9"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"0b4fad27-0f32-4d03-9d6c-31b09101d72e","level":"error","message":"Failed to process assessment job","processingTime":"80713ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"f64429f3-6c76-427f-94c7-a65f3f58c3b9"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"0b4fad27-0f32-4d03-9d6c-31b09101d72e","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:44"}
{"error":"Request failed with status code 400","jobId":"272ad54d-8fd2-4a61-be34-e4937f063f25","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"77e61f7f-fa83-43c8-b45d-b90eaa0b488a"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"272ad54d-8fd2-4a61-be34-e4937f063f25","level":"error","message":"Failed to process assessment job","processingTime":"80459ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"77e61f7f-fa83-43c8-b45d-b90eaa0b488a"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"272ad54d-8fd2-4a61-be34-e4937f063f25","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:44"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"f7fde668-f823-4666-b49b-24320ef69fa3","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"c78e41a3-7a8d-486f-8b5e-3d32390138a5"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44","url":"/jobs/f7fde668-f823-4666-b49b-24320ef69fa3/status"}
{"error":"Request failed with status code 400","jobId":"f7fde668-f823-4666-b49b-24320ef69fa3","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"10ee28ba-1708-4d3e-a6c0-f263cdec4fca","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"dabbf2c4-343a-4240-97d0-d9acb75cf93e"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44","url":"/jobs/10ee28ba-1708-4d3e-a6c0-f263cdec4fca/status"}
{"error":"Request failed with status code 400","jobId":"10ee28ba-1708-4d3e-a6c0-f263cdec4fca","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44"}
{"error":"Request failed with status code 400","jobId":"f7fde668-f823-4666-b49b-24320ef69fa3","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"c78e41a3-7a8d-486f-8b5e-3d32390138a5"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"f7fde668-f823-4666-b49b-24320ef69fa3","level":"error","message":"Failed to process assessment job","processingTime":"81227ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"c78e41a3-7a8d-486f-8b5e-3d32390138a5"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"f7fde668-f823-4666-b49b-24320ef69fa3","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:44"}
{"error":"Request failed with status code 400","jobId":"10ee28ba-1708-4d3e-a6c0-f263cdec4fca","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"dabbf2c4-343a-4240-97d0-d9acb75cf93e"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"10ee28ba-1708-4d3e-a6c0-f263cdec4fca","level":"error","message":"Failed to process assessment job","processingTime":"80912ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"dabbf2c4-343a-4240-97d0-d9acb75cf93e"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"10ee28ba-1708-4d3e-a6c0-f263cdec4fca","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:44"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"ec14e711-fb0c-4f38-aa85-bd075db4380c","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"037517a8-306d-4773-aa2d-36f9bb1bfb61"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44","url":"/jobs/ec14e711-fb0c-4f38-aa85-bd075db4380c/status"}
{"error":"Request failed with status code 400","jobId":"ec14e711-fb0c-4f38-aa85-bd075db4380c","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:44"}
{"error":"Request failed with status code 400","jobId":"ec14e711-fb0c-4f38-aa85-bd075db4380c","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"037517a8-306d-4773-aa2d-36f9bb1bfb61"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"ec14e711-fb0c-4f38-aa85-bd075db4380c","level":"error","message":"Failed to process assessment job","processingTime":"81176ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:44","userId":"037517a8-306d-4773-aa2d-36f9bb1bfb61"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"ec14e711-fb0c-4f38-aa85-bd075db4380c","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:44"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:45","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:45","userId":"40cd752c-480d-4d15-9169-afc895e2b47c"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:45","url":"/jobs/2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0/status"}
{"error":"Request failed with status code 400","jobId":"2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:45"}
{"error":"Request failed with status code 400","jobId":"2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:45","userId":"40cd752c-480d-4d15-9169-afc895e2b47c"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0","level":"error","message":"Failed to process assessment job","processingTime":"81855ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:45","userId":"40cd752c-480d-4d15-9169-afc895e2b47c"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"2c96a1d9-f0a1-4d6f-943c-a6c7f0bb31f0","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:45"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:48","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:48","userId":"fa613df4-9f6d-4a53-99c6-6fd174d07645"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:48","url":"/jobs/a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5/status"}
{"error":"Request failed with status code 400","jobId":"a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:48"}
{"error":"Request failed with status code 400","jobId":"a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:48","userId":"fa613df4-9f6d-4a53-99c6-6fd174d07645"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5","level":"error","message":"Failed to process assessment job","processingTime":"85319ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:48","userId":"fa613df4-9f6d-4a53-99c6-6fd174d07645"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"a6c9d624-23e4-4a69-bf98-f9c8fc8e64c5","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:48"}
{"error":"timeout of 30000ms exceeded","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-20 12:10:49","url":"/results"}
{"error":"timeout of 30000ms exceeded","jobId":"eb5ac948-12a1-4edc-bba5-52f7292740a2","level":"error","message":"Failed to save failed analysis result","service":"analysis-worker","timestamp":"2025-07-20 12:10:49","userId":"ffc8fb51-8ec4-4c95-b912-8a8674edbce1"}
{"error":"Request failed with status code 400","level":"error","message":"Archive service response error","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:49","url":"/jobs/eb5ac948-12a1-4edc-bba5-52f7292740a2/status"}
{"error":"Request failed with status code 400","jobId":"eb5ac948-12a1-4edc-bba5-52f7292740a2","level":"error","message":"Failed to update analysis job status","service":"analysis-worker","status":400,"statusText":"Bad Request","timestamp":"2025-07-20 12:10:49"}
{"error":"Request failed with status code 400","jobId":"eb5ac948-12a1-4edc-bba5-52f7292740a2","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-20 12:10:49","userId":"ffc8fb51-8ec4-4c95-b912-8a8674edbce1"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"eb5ac948-12a1-4edc-bba5-52f7292740a2","level":"error","message":"Failed to process assessment job","processingTime":"85185ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-20 12:10:49","userId":"ffc8fb51-8ec4-4c95-b912-8a8674edbce1"}
{"error":"Assessment processing failed: timeout of 30000ms exceeded","jobId":"eb5ac948-12a1-4edc-bba5-52f7292740a2","level":"error","maxRetries":3,"message":"Error should not be retried (AI service error), sending to dead letter queue","retryCount":1,"service":"analysis-worker","shouldNotRetry":true,"timestamp":"2025-07-20 12:10:49"}
